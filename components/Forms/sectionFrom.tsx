"use client";
import FormContainer from "@/components/custom/form-container";
import { Label } from "@/components/ui/label";
import { useFormContext } from "react-hook-form";
import { useEffect, useMemo, useState } from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { Skeleton } from "../ui/skeleton";
import { useProgress } from "@/lib/progress-context";
import TimelineSession from "../custom/timeline-session";
import { useRouter, useSearchParams } from "next/navigation";
import { useFile } from "@/hooks/useFile";
import {
  getAccessToken,
  getAllSectionForms,
  getOapForm,
  getOapFormSections,
  getStudentDetails,
  saveOapForm,
  getOapDetail,
} from "@/api/api";
import Header from "../custom/header";
import { useAtom } from "jotai";
import {
  appHeroAtom,
  applicationId,
  consumerAPIKey,
  dateReplacement,
  email,
  fontSizeAtom,
  nextForm,
  preferredDateFormat,
  preferredLanguage,
  programmeName,
  routes,
  staticContentsAtom,
} from "@/lib/atom";
import { toast } from "react-hot-toast";
import { AlertCircle, ChevronDown, Watch } from "lucide-react";
import MobileTimelineSession from "../custom/mobile-timeline-session";
import Image from "next/image";
import loader2 from "../../public/loader2.svg";
import { signOut } from "aws-amplify/auth";
import { isValidPhoneNumber } from "libphonenumber-js";
import LanguageSelector from "../custom/translate";
import AlreadySubmittedModal from "../custom/already-submitted-modal";
import SavingModal from "../custom/saving-modal";
import { getBrandSpecificFontStyle } from "@/lib/brandUtils";

function SectionalForm() {
  const router = useRouter();

  const queryClient = useQueryClient();
  const [staticContents, setStaticContents] = useAtom(staticContentsAtom);

  const [validationError, setValidationError] = useState<any>();
  const [saving, setSaving] = useState<boolean>(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isAlreadySubmittedModalOpen, setIsAlreadySubmittedModalOpen] =
    useState(false);
  const { setProgress, progress } = useProgress();
  const [subSectionArrayMap, setSubSectionArrayMap] = useState<
    Record<string, number[]>
  >({});
  const [subSectionCountMap, setSubSectionCountMap] = useState<
    Record<string, number>
  >({});
  const [nextFormDetails, setNextFormDetails]: any = useAtom(nextForm);
  const [userEmail] = useAtom(email);
  const [application] = useAtom(applicationId);
  const [route] = useAtom(routes);
  const [apiKey] = useAtom(consumerAPIKey);
  const [, setProgrammeName] = useAtom(programmeName);
  const [preferLang, setPreferLang] = useAtom(preferredLanguage);
  const [triggerError, setTriggerError] = useState(false);
  const [isAppHero] = useAtom(appHeroAtom);
  const [, setPreferredDateFormat] = useAtom(preferredDateFormat);
  const [, setFontSize] = useAtom(fontSizeAtom);
  const [, setDateReplacement] = useAtom(dateReplacement);

  const {
    register,
    control,
    setValue,
    watch,
    formState: { errors },
    clearErrors,
    trigger,
    setError,
    getValues,
    reset,
    resetField,
  } = useFormContext();

  const { uploadFile, deleteFile } = useFile();

  const reverseTransformDropdowns = (
    formData: Record<string, string | string[] | undefined>
  ) => {
    const reversedData: Record<string, string | string[] | undefined> = {};

    for (const [key, value] of Object.entries(formData)) {
      reversedData[key] = value;
    }

    return reversedData;
  };

  const searchParams = useSearchParams();
  const apply: any = searchParams?.get("apply");
  const step: any = searchParams?.get("step");
  const [fontSize] = useAtom(fontSizeAtom);

  const { data: pageQuery } = useQuery({
    queryKey: [`${nextFormDetails?.oap}-${nextFormDetails?.mode}`],
    queryFn: async () => {
      return await getOapDetail(
        {
          name: nextFormDetails?.oap,
          mode: nextFormDetails?.mode,
          ...(preferLang === "de" && { language: "de" }),
        },
        apiKey
      );
    },
    enabled: !!nextFormDetails?.oap && !!nextFormDetails?.mode,
  });

  // Memoized values to prevent unnecessary re-renders
  const memoizedStaticContents = useMemo(
    () => pageQuery?.staticContents,
    [pageQuery?.staticContents]
  );

  const memoizedDateFormat = useMemo(
    () => pageQuery?.preferedDateFormat,
    [pageQuery?.preferedDateFormat]
  );

  const memoizedDateReplacement = useMemo(
    () => pageQuery?.replaceWith,
    [pageQuery?.replaceWith]
  );

  const memoizedFontSize = useMemo(
    () => pageQuery?.fontSize,
    [pageQuery?.fontSize]
  );

  useEffect(() => {
    if (pageQuery && isAppHero) {
      setStaticContents(memoizedStaticContents);
      setPreferredDateFormat(memoizedDateFormat);
      setDateReplacement(memoizedDateReplacement);
      if (memoizedFontSize) {
        setFontSize(memoizedFontSize);
      }
    }
  }, [
    pageQuery,
    isAppHero,
    memoizedStaticContents,
    memoizedDateFormat,
    memoizedDateReplacement,
    memoizedFontSize,
    setStaticContents,
    setPreferredDateFormat,
    setDateReplacement,
    setFontSize,
  ]);

  const enableAutoDetection = useMemo(
    () => pageQuery?.enableAutoDetection,
    [pageQuery?.enableAutoDetection]
  );

  const {
    data: studentDetails,
    refetch: refetchStudent,
    isFetching: studentDetailsIsFetching,
  } = useQuery({
    queryKey: [`${userEmail}-form`],
    queryFn: async () => {
      if (userEmail) {
        let res: any = await getStudentDetails(
          nextFormDetails?.oap,
          userEmail,
          application,
          apiKey,
          await getAccessToken()
        );
        setProgrammeName(res?.programDisplayName);

        return res;
      } else {
        return;
      }
    },
    enabled: !!(nextFormDetails && userEmail && application),
  });

  useEffect(() => {
    let temp: any = queryClient.getQueryData([`${application}-form`]);

    if (temp) {
      setProgrammeName(temp?.programDisplayName);
      Object.entries(reverseTransformDropdowns(temp)).forEach(([k, val]) => {
        setValue(k, val);
      });
    }
  }, [userEmail, application, studentDetails]);
  const {
    data: sectionQuery,
    isFetching: sectionQueryIsFetching,
    refetch: refetchSectionQuery,
  } = useQuery({
    queryKey: [
      `${nextFormDetails?.oap}-${nextFormDetails?.mode}-${nextFormDetails?.[apply]}-form`,
    ],
    queryFn: async () => {
      if (nextFormDetails?.type == "single") {
        if (
          nextFormDetails?.form &&
          nextFormDetails?.oap &&
          nextFormDetails?.mode
        ) {
          let res = await getOapForm(
            {
              oap: nextFormDetails?.oap,
              form: nextFormDetails?.form,
              mode: nextFormDetails?.mode,
              ...(preferLang === "de" && { language: "de" }),
            },
            apiKey,
            await getAccessToken()
          );
          getFiltered(res?.section, studentDetails);
          return res;
        }
      } else {
        if (
          nextFormDetails?.[apply] &&
          nextFormDetails?.oap &&
          nextFormDetails?.mode
        ) {
          let res = await getOapForm(
            {
              oap: nextFormDetails?.oap,
              form: nextFormDetails?.[apply],
              mode: nextFormDetails?.mode,
              ...(preferLang === "de" && { language: "de" }),
            },
            apiKey,
            await getAccessToken()
          );
          getFiltered(res?.section, studentDetails);
          return res;
        } else {
          return;
        }
      }
    },
    enabled: true && !!studentDetails,
  });

  const { data: allSections, refetch: refetchAllSections } = useQuery({
    queryKey: [`all-section-forms`],
    queryFn: async () => {
      const res = await getAllSectionForms(
        {
          oapName: nextFormDetails?.oap,
          formName:
            nextFormDetails?.type === "single"
              ? nextFormDetails?.form
              : nextFormDetails?.[apply],
          mode: nextFormDetails?.mode,
          ...(preferLang === "de" && { language: "de" }),
        },
        apiKey,
        await getAccessToken()
      );
      if (res?.length > 0) {
        let temp = getFiltered(res, studentDetails);
        localStorage.setItem("sections", JSON.stringify(temp));
        return temp;
      }
      return [];
    },
    enabled:
      !!apiKey &&
      !!nextFormDetails &&
      !!Object.keys(nextFormDetails).length &&
      !!studentDetails,
  });

  function checkVisibleWhen(visibleWhenProps: any, dataTransformed: any) {
    if (
      !visibleWhenProps ||
      !visibleWhenProps.condition ||
      !Array.isArray(visibleWhenProps.rules)
    ) {
      return true; // no conditions -> visible by default
    }
    const { condition, rules } = visibleWhenProps;

    if (condition === "and") {
      // All rules must match
      return rules.every((rule: any) => {
        return dataTransformed?.[rule.fieldName] === rule.value;
      });
    }

    if (condition === "notAnd") {
      // All rules must match, but we invert the result
      const allMatch = rules.every((rule: any) => {
        return dataTransformed?.[rule.fieldName] === rule.value;
      });
      return !allMatch;
    }

    return true;
  }

  const getFiltered = (sectionData: any, student: any) => {
    let temp = sectionData.filter((item: any) => {
      if (item?.visibleWhen) {
        let obj = item.visibleWhen;
        const watchedValue =
          watch(obj?.fieldName)?.value || student?.[obj?.fieldName];

        if (obj?.condition === "notEqual") {
          if (typeof obj.value === "string") {
            const result = watchedValue !== obj.value;
            if (result) {
              const fields = Array.isArray(obj?.resetSectionFields)
                ? obj.resetSectionFields
                : [obj.resetSectionFields];
              fields.forEach((field: any) => resetField(field));
            }
            return result;
          }

          if (Array.isArray(obj.value)) {
            const valuesToCheck = obj.value.map((v: any) => v.value);
            // For notEqual condition: section should be visible only if a value is selected AND it's not in the excluded values
            const result =
              watchedValue && !valuesToCheck.includes(watchedValue);
            if (!result) {
              const fields = Array.isArray(obj?.resetSectionFields)
                ? obj.resetSectionFields
                : [obj.resetSectionFields];
              fields.forEach((field: any) => resetField(field));
            }
            return result ? item : false;
          }

          const result = watchedValue !== obj.value;
          if (result) {
            const fields = Array.isArray(obj?.resetSectionFields)
              ? obj.resetSectionFields
              : [obj.resetSectionFields];
            fields.forEach((field: any) => resetField(field));
          }
          return result;
        }

        if (watch(obj?.fieldName)?.value) {
          const result = watchedValue === obj?.value;
          if (result) {
            const fields = Array.isArray(obj?.resetSectionFields)
              ? obj.resetSectionFields
              : [obj.resetSectionFields];
            fields.forEach((field: any) => resetField(field));
          }
          return result;
        }
        return student?.[obj?.fieldName] == obj?.value ? item : false;
      } else return item;
    });

    temp.sort((a: any, b: any) => a.displayOrder - b.displayOrder);
    return temp;
  };
  const responses: any = useMemo(() => {
    if (allSections) {
      return getFiltered(allSections, studentDetails);
    }
    return [];
  }, [allSections]);

  const {
    data: formQuery,
    isFetching: formQueryIsFetching,
    refetch: refetchFormQuery,
  } = useQuery({
    // next sections page
    queryKey: [
      `${nextFormDetails?.oap}-${apply}-${step}-${sectionQuery?.SK}-${sectionQuery?.section?.[step]?.name}`,
    ],
    queryFn: async () => {
      if (!(sectionQuery?.section?.[step]?.section && sectionQuery?.form))
        return;
      let temp = getFiltered(sectionQuery?.section, studentDetails);
      sectionQuery.section = temp;
      let res = await getOapFormSections(
        {
          oap: nextFormDetails?.oap,
          mode: nextFormDetails?.mode,
          formName: sectionQuery?.form,
          sectionName: sectionQuery?.section?.[step]?.section,
          ...(preferLang === "de" && { language: "de" }),
        },
        apiKey,
        await getAccessToken()
      );

      // checking the number of sub section for each repeatable section
      let newSubSectionArrayMap: Record<string, number[]> = {};
      let newSubSectionCountMap: Record<string, number> = {};

      res?.fieldData?.forEach((item: any) => {
        if (item?.sectionCanRepeat) {
          const fieldName = item?.fieldName;
          const length = studentDetails?.[fieldName]?.length || 0;
          let obj = item?.minLengthWhen;
          let requiredLength =
            studentDetails[obj?.fieldName] == obj?.value &&
            studentDetails[obj?.fieldName] !== undefined
              ? obj?.minLength
              : item?.minLength;

          const iterations = length === 0 ? requiredLength : length;

          // Initialize array for this specific field
          newSubSectionArrayMap[fieldName] = [];

          for (let i = 0; i < iterations; i++) {
            newSubSectionArrayMap[fieldName].push(i);
          }

          // Set the count for this field
          newSubSectionCountMap[fieldName] =
            newSubSectionArrayMap[fieldName].length;
        }
      });

      setSubSectionArrayMap(newSubSectionArrayMap);
      setSubSectionCountMap(newSubSectionCountMap);
      let tempObj = { ...studentDetails, ...getValues() };
      Object.entries(reverseTransformDropdowns(tempObj)).forEach(([k, val]) => {
        setValue(k, val);
      });
      return res;
    },
    enabled: true && !!sectionQuery && !!studentDetails,
  });

  useEffect(() => {
    if (
      sectionQuery &&
      !formQueryIsFetching &&
      !sectionQueryIsFetching &&
      studentDetails?.applicationStatus === "submitted"
    ) {
      setIsAlreadySubmittedModalOpen(true);
    }
  }, [
    studentDetails,
    sectionQuery,
    formQueryIsFetching,
    sectionQueryIsFetching,
  ]);

  useEffect(() => {
    const getValidate = async () => {
      if (
        validationError?.some(
          (item: any) => item?.missingFieldsExist === true
        ) &&
        !formQueryIsFetching &&
        !sectionQueryIsFetching &&
        triggerError
      ) {
        await trigger();
      }
    };

    getValidate();
  }, [
    validationError,
    trigger,
    step,
    formQueryIsFetching,
    sectionQueryIsFetching,
  ]);

  const getSideBarStatus = (sectionName: any) => {
    const resultArray: any = [];
    responses?.forEach((section: any) => {
      const sectionResult = {
        displayName: section.displayName,
        missingFieldsCount: 0,
        missingFieldsExist: false,
      };
      if (section?.displayName !== "Review & Submit" || section?.validate) {
        section?.fieldData?.forEach((field: any) => {
          const fieldName = field?.fieldName;
          const isRequired = field?.required;
          const subSection = field?.subSection;
          const subSectionRepeat = field?.sectionCanRepeat;

          if (field.requiredWhen) {
            const isNotValid = validateRequiredWhen(
              studentDetails,
              field.requiredWhen
            );

            if (isNotValid !== undefined && !isNotValid) {
              sectionResult.missingFieldsCount++;
              sectionResult.missingFieldsExist = true;
            } else {
              studentDetails[field?.requiredWhenFieldName] = true;
            }
          }
          if (
            subSection &&
            subSectionRepeat &&
            checkVisibleWhen(field?.visibleWhen, studentDetails)
          ) {
            field?.[subSection]?.fieldData?.map((section: any) => {
              const fieldData = section?.fieldName;
              const required = section?.required;

              if (studentDetails?.[subSection]) {
                studentDetails?.[subSection]?.map(
                  (item: any, itemIndex: number) => {
                    let visibleWhen = true;

                    if (section?.visibleWhen) {
                      const { condition, rules } = section.visibleWhen;

                      if (condition === "notAnd" && Array.isArray(rules)) {
                        const allMatch = rules.every((rule: any) => {
                          return studentDetails[rule.fieldName] === rule.value;
                        });
                        visibleWhen = !allMatch;
                      } else if (Array.isArray(section.visibleWhen)) {
                        visibleWhen = section.visibleWhen.some(
                          (condition: any) =>
                            item[condition?.fieldName] === condition?.value
                        );
                      } else {
                        visibleWhen =
                          item[section?.visibleWhen?.fieldName] ===
                          section?.visibleWhen?.value;
                      }
                    }

                    if (
                      item[fieldData] &&
                      typeof item[fieldData] === "string" &&
                      item[fieldData]?.trim() === ""
                    ) {
                      setValue(`${subSection}.${itemIndex}.${fieldData}`, "");
                      delete item[fieldData];
                    }

                    if (required && !(fieldData in item) && visibleWhen) {
                      sectionResult.missingFieldsCount++;
                      sectionResult.missingFieldsExist = true;
                    }
                  }
                );
              }
            });
          }

          if (subSection && !subSectionRepeat) {
            const visibleWhen = field?.visibleWhen
              ? studentDetails[field?.visibleWhen?.fieldName] ===
                field?.visibleWhen?.value
              : true;

            field?.[subSection]?.fieldData?.map((section: any) => {
              const fieldData = section?.fieldName;
              let required = section?.required;
              const mandatoryWhen = section?.mandatoryWhen;
              const regex = section?.rules?.pattern;
              if (mandatoryWhen) {
                if (
                  mandatoryWhen?.condition == "greaterThan" &&
                  mandatoryWhen?.fieldValue <=
                    studentDetails[mandatoryWhen?.fieldName]
                ) {
                  required = true;
                } else if (mandatoryWhen?.condition == "notEqualTo") {
                  if (
                    mandatoryWhen?.fieldValue !==
                    studentDetails?.[mandatoryWhen?.fieldName]
                  ) {
                    required = true;
                  } else if (
                    mandatoryWhen?.fieldValue ===
                    studentDetails?.[mandatoryWhen?.fieldName]
                  ) {
                    required = false;
                  }
                }
              }
              let visibleWhenUnderSection: boolean;
              if (Array.isArray(section?.visibleWhen?.value)) {
                if (section?.visibleWhen?.condition === "notEqual") {
                  visibleWhenUnderSection = section?.visibleWhen
                    ? studentDetails[section?.visibleWhen?.fieldName] &&
                      !section?.visibleWhen?.value?.some(
                        (item: { value: string; label: string }) =>
                          item.value ===
                          studentDetails[section?.visibleWhen?.fieldName]
                      )
                    : true;
                } else {
                  visibleWhenUnderSection = section?.visibleWhen
                    ? section?.visibleWhen?.value?.includes(
                        studentDetails[section?.visibleWhen?.fieldName]
                      )
                    : true;
                }
              } else {
                if (section?.visibleWhen?.condition === "notEqual") {
                  visibleWhenUnderSection = section?.visibleWhen
                    ? studentDetails[section?.visibleWhen?.fieldName] &&
                      !section?.visibleWhen?.value?.some(
                        (item: { value: string; label: string }) =>
                          item.value ===
                          studentDetails[section?.visibleWhen?.fieldName]
                      )
                    : true;
                } else {
                  visibleWhenUnderSection = section?.visibleWhen
                    ? studentDetails[section?.visibleWhen?.fieldName] ===
                      section?.visibleWhen?.value
                    : true;
                }
              }

              if (
                studentDetails?.[fieldData] &&
                typeof studentDetails[fieldData] === "string" &&
                studentDetails[fieldData]?.trim() === ""
              ) {
                setValue(fieldData, "");
                delete studentDetails[fieldData];
              }

              if (
                required &&
                !(fieldData in studentDetails) &&
                visibleWhenUnderSection &&
                visibleWhen
              ) {
                sectionResult.missingFieldsCount++;
                sectionResult.missingFieldsExist = true;
              }
            });
          } else {
            if (field?.mandatoryWhen) {
              const mandatoryWhen = field?.mandatoryWhen;
              let isMandatory = false;

              if (
                mandatoryWhen?.condition === "or" &&
                Array.isArray(mandatoryWhen?.rules)
              ) {
                isMandatory = mandatoryWhen.rules.some((ruleGroup: any) => {
                  if (
                    ruleGroup.condition === "and" &&
                    Array.isArray(ruleGroup.rules)
                  ) {
                    return ruleGroup.rules.every((rule: any) => {
                      const fieldValue =
                        (rule?.subSectionName &&
                          studentDetails?.[rule.subSectionName]?.some(
                            (obj: any) => obj[rule.fieldName] === rule.value
                          )) ||
                        studentDetails?.[rule.fieldName] ||
                        watch(rule.fieldName);
                      return typeof fieldValue === "boolean"
                        ? fieldValue
                        : fieldValue?.value === rule.value ||
                            fieldValue === rule.value;
                    });
                  }
                  return false;
                });
              }

              // Check if the field is mandatory but has no value
              if (isMandatory) {
                const fieldValue = studentDetails?.[fieldName];
                const isEmpty =
                  !fieldValue ||
                  (Array.isArray(fieldValue) && fieldValue.length === 0) ||
                  fieldValue === "";

                if (isEmpty) {
                  // Increase the missing fields count
                  sectionResult.missingFieldsCount++;
                  sectionResult.missingFieldsExist = true;
                }
              }
            }

            if (
              studentDetails[fieldName] &&
              typeof studentDetails[fieldName] === "string" &&
              studentDetails[fieldName]?.trim() === ""
            ) {
              setValue(fieldName, "");
              delete studentDetails[fieldName];
            }

            if (
              isRequired &&
              fieldName &&
              !field?.visibleWhen &&
              (studentDetails[fieldName] === "" ||
                studentDetails[fieldName] === false)
            ) {
              sectionResult.missingFieldsCount++;
              sectionResult.missingFieldsExist = true;
            }

            const visibleWhen = checkVisibility(field.visibleWhen);

            if (
              isRequired &&
              !(fieldName in studentDetails) &&
              visibleWhen &&
              checkVisibleWhen(field?.visibleWhen, studentDetails)
            ) {
              sectionResult.missingFieldsCount++;
              sectionResult.missingFieldsExist = true;
            }
          }
        });
        resultArray.push(sectionResult);
      }
    });

    const currentSectionName = resultArray?.find(
      (item: any) => item?.displayName === sectionName
    );
    if (currentSectionName?.missingFieldsExist === false) {
      return "completed";
    } else return "";
  };

  useEffect(() => {
    const saveFormData = async () => {
      if (apiKey && Object.keys(nextFormDetails).length > 0 && studentDetails) {
        refetchAllSections();
        refetchSectionQuery();
      }
    };

    if (
      sectionQuery?.dependencies?.refetchAllSections &&
      formQuery?.fieldData?.some(
        (item: any) =>
          item?.fieldName === sectionQuery?.dependencies?.refetchAllSections
      )
    ) {
      saveFormData();
    }
  }, [watch(sectionQuery?.dependencies?.refetchAllSections)]);

  useEffect(() => {
    if (apiKey && Object.keys(nextFormDetails).length > 0 && studentDetails) {
      refetchAllSections();
      refetchFormQuery();
      refetchSectionQuery();
      refetchStudent();
    }
  }, [preferLang]);

  interface DropdownItem {
    label: string;
    //@ts-ignore
    value: any;
  }
  const transformDropdowns = (
    formData: Record<string, unknown>
  ): Record<string, any> => {
    const transformedData: Record<string, any> = {};

    for (const [key, value] of Object.entries(formData)) {
      if (Array.isArray(value) && typeof value[0] == "object") {
        let newValue = [...value];

        newValue?.map((item, i) => {
          for (const [key1, value1] of Object.entries(item)) {
            if (value1 && typeof value1 === "object") {
              if (!("label" in value1) && !("value" in value1)) {
                newValue[i][key1] = value1;
              } else {
                const labelKey = `${key1}DisplayName`;
                newValue[i][labelKey] = (value1 as DropdownItem).label;
                newValue[i][key1] = (value1 as DropdownItem).value;
              }
            } else if (value1 === "" || value1 === undefined) {
              delete newValue[i][key1];
            } else if (typeof value1 !== "object") {
              newValue[i][key1] = value1;
            }
          }
        });
        transformedData[key] = newValue;
      } else if (value && typeof value === "object") {
        if (!("label" in value) && !("value" in value)) {
          transformedData[key] = value;
          if (Object.keys(value).length == 0) {
            delete transformedData[key];
          }
        } else {
          const labelKey = `${key}DisplayName`;
          transformedData[labelKey] = (value as DropdownItem).label;
          transformedData[key] = (value as DropdownItem).value;
        }
      } else if (value && typeof value !== "object" && value !== "") {
        transformedData[key] = value === "on" ? !!value : value;
      } else if (
        !isNaN(value as number) &&
        typeof value !== "object" &&
        value !== ""
      ) {
        transformedData[key] = value;
      }
    }
    // this part is for subsection or documents upload
    let dataTransformed = attachDocId(transformedData, formQuery?.fieldData);
    return dataTransformed;
  };

  const attachDocId = (rawData: any, dataList: any[]) => {
    dataList?.forEach((item: any) => {
      if (item?.type === "document") {
        if (rawData[item?.fieldName]?.[0]?.hasOwnProperty("documentId")) {
          rawData[item?.fieldName] = rawData[item?.fieldName];
        } else rawData[item?.fieldName] = [];
        if (rawData[item?.fieldName]?.length == 0) {
          delete rawData[item?.fieldName];
        }
      } else if (item?.type === "multiDocument") {
        if (Array.isArray(rawData?.[item?.fieldName])) {
          let temp = rawData?.[item?.fieldName]?.map((e: any) => e);
          rawData[item?.fieldName] = temp;
        }
        if (Object.keys(rawData[item?.fieldName] || {}).length === 0) {
          delete rawData[item?.fieldName];
        }
      }
      if (item?.type == "subsection" && item?.sectionCanRepeat) {
        rawData[item?.fieldName]?.forEach((e: any, index: number) => {
          attachDocId(e, item?.[item?.fieldName]?.fieldData);
        });
      }
      if (item?.type == "subsection" && !item?.sectionCanRepeat) {
        attachDocId(rawData, item?.[item?.fieldName]?.fieldData);
      }
    });
    return rawData;
  };

  function validateRequiredWhen(
    dataTransformed: any,
    requiredWhen: Array<any>
  ): any {
    if (
      !dataTransformed.priorInstitutionsAttended ||
      !Array.isArray(dataTransformed.priorInstitutionsAttended)
    ) {
      return;
    }

    for (const condition of requiredWhen) {
      const { hasValue, hasValueFieldName, notHasValueFieldName, notHasValue } =
        condition;

      if (dataTransformed[hasValueFieldName] === hasValue) {
        if (
          dataTransformed.priorInstitutionsAttended.some(
            (item: any) => item[notHasValueFieldName] === notHasValue
          )
        ) {
          return true;
        } else {
          return false;
        }
      }
    }
  }

  function isFieldVisible(field: any, dataTransformed: any) {
    if (!field?.visibleWhen?.fieldName || !field?.visibleWhen?.value) {
      return false;
    }

    const fieldName = field.visibleWhen.fieldName;
    const expectedValue = field.visibleWhen.value;

    const influencedArray = dataTransformed[fieldName];

    return (
      Array.isArray(influencedArray) &&
      influencedArray.some((item) => item.value === expectedValue)
    );
  }

  const validateOnSubmit = (
    dataTransformed: any,
    canTriggerError?: boolean
  ) => {
    const resultArray: any = [];

    responses?.forEach((section: any) => {
      const sectionResult = {
        displayName: section.displayName,
        missingFieldsCount: 0,
        missingFieldsExist: false,
        isValidateRegex: true,
      };
      if (section?.displayName !== "Review & Submit" || section?.validate) {
        section?.fieldData?.forEach((field: any) => {
          const fieldName = field?.fieldName;
          const isRequired = field?.required;
          const regex = field?.rules?.pattern;
          const subSection = field?.subSection;
          const subSectionRepeat = field?.sectionCanRepeat;
          const visibleWhenProps = field?.visibleWhen;

          if (canTriggerError && field?.requiredWhen) {
            const isNotValid = validateRequiredWhen(
              dataTransformed,
              field.requiredWhen
            );

            if (isNotValid !== undefined && !isNotValid) {
              sectionResult.missingFieldsCount++;
              sectionResult.missingFieldsExist = true;
              dataTransformed[field?.requiredWhenFieldName] = false;
              setValue(field?.requiredWhenFieldName, false);
              setError(field?.requiredWhenFieldName, field.requiredWhenRules);
            } else {
              dataTransformed[field?.requiredWhenFieldName] = true;
              setValue(field?.requiredWhenFieldName, true);
              clearErrors([field?.requiredWhenFieldName]);
            }
          }

          if (
            subSection &&
            subSectionRepeat &&
            checkVisibleWhen(visibleWhenProps, dataTransformed)
          ) {
            let i = 0;
            field?.[subSection]?.fieldData?.map(
              (section: any, sectionIndex: number) => {
                // Use sectionIndex in a key if this is rendering JSX
                const fieldData = section?.fieldName;
                const required = section?.required;
                const regex = section?.rules?.pattern;

                if (dataTransformed?.[subSection]) {
                  dataTransformed?.[subSection]?.map(
                    (item: any, itemIndex: number) => {
                      // Use itemIndex in a key if this is rendering JSX
                      let visibleWhen = true;

                      // New: handle 'visibleWhen' with condition types
                      if (section?.visibleWhen) {
                        const { condition, rules } = section.visibleWhen;

                        if (condition === "notAnd" && Array.isArray(rules)) {
                          const allMatch = rules.every((rule: any) => {
                            return (
                              studentDetails[rule.fieldName] === rule.value
                            );
                          });

                          visibleWhen = !allMatch;
                        } else if (Array.isArray(section.visibleWhen)) {
                          visibleWhen = section.visibleWhen.some(
                            (condition: any) =>
                              item[condition?.fieldName] === condition?.value
                          );
                        } else {
                          visibleWhen =
                            item[section?.visibleWhen?.fieldName] ===
                            section?.visibleWhen?.value;
                        }
                      }

                      if (
                        item[fieldData] &&
                        typeof item[fieldData] === "string" &&
                        item[fieldData]?.trim() === ""
                      ) {
                        setValue(`${subSection}.${itemIndex}.${fieldData}`, "");
                        delete item[fieldData];
                      }

                      // Required field check
                      if (required && !(fieldData in item) && visibleWhen) {
                        sectionResult.missingFieldsCount++;
                        sectionResult.missingFieldsExist = true;
                      }

                      // Phone number validation
                      if (
                        (field?.type === "number" || field.type === "mobile") &&
                        item[fieldData]?.numberWithCode &&
                        !isValidPhoneNumber(item[fieldData]?.numberWithCode)
                      ) {
                        setValue(fieldData, "");
                        delete item[fieldData];
                        if (!triggerError) {
                          clearErrors();
                        }
                      }

                      // Regex pattern validation
                      if (
                        regex &&
                        item[fieldData] &&
                        !new RegExp(regex?.value).test(item[fieldData])
                      ) {
                        sectionResult.missingFieldsCount++;
                        sectionResult.missingFieldsExist = true;
                        sectionResult.isValidateRegex = false;
                        setValue(fieldData, "");
                        delete item[fieldData];
                        if (!triggerError) {
                          clearErrors();
                        }
                      }
                    }
                  );
                }
              }
            );
          }

          if (subSection && !subSectionRepeat) {
            const visibleWhen = field?.visibleWhen
              ? dataTransformed[field?.visibleWhen?.fieldName] ===
                field?.visibleWhen?.value
              : true;

            field?.[subSection]?.fieldData?.map(
              (section: any, sectionIndex: number) => {
                // Use sectionIndex in a key if this is rendering JSX
                const fieldData = section?.fieldName;
                let required = section?.required;
                const mandatoryWhen = section?.mandatoryWhen;
                const regex = section?.rules?.pattern;
                if (mandatoryWhen) {
                  if (
                    mandatoryWhen?.condition == "greaterThan" &&
                    mandatoryWhen?.fieldValue <=
                      dataTransformed[mandatoryWhen?.fieldName]
                  ) {
                    required = true;
                  } else if (mandatoryWhen?.condition == "notEqualTo") {
                    if (
                      mandatoryWhen?.fieldValue !==
                      dataTransformed?.[mandatoryWhen?.fieldName]
                    ) {
                      required = true;
                    } else if (
                      mandatoryWhen?.fieldValue ===
                      dataTransformed?.[mandatoryWhen?.fieldName]
                    ) {
                      required = false;
                    }
                  }
                }
                let visibleWhenUnderSection: boolean;
                if (Array.isArray(section?.visibleWhen?.value)) {
                  if (section?.visibleWhen?.condition === "notEqual") {
                    visibleWhenUnderSection = section?.visibleWhen
                      ? dataTransformed[section?.visibleWhen?.fieldName] &&
                        !section?.visibleWhen?.value?.some(
                          (item: { value: string; label: string }) =>
                            item.value ===
                            dataTransformed[section?.visibleWhen?.fieldName]
                        )
                      : true;
                  } else {
                    visibleWhenUnderSection = section?.visibleWhen
                      ? section?.visibleWhen?.value?.includes(
                          dataTransformed[section?.visibleWhen?.fieldName]
                        )
                      : true;
                  }
                } else {
                  if (section?.visibleWhen?.condition === "notEqual") {
                    visibleWhenUnderSection = section?.visibleWhen
                      ? dataTransformed[section?.visibleWhen?.fieldName] &&
                        !section?.visibleWhen?.value?.some(
                          (item: { value: string; label: string }) =>
                            item.value ===
                            dataTransformed[section?.visibleWhen?.fieldName]
                        )
                      : true;
                  } else {
                    visibleWhenUnderSection = section?.visibleWhen
                      ? dataTransformed[section?.visibleWhen?.fieldName] ===
                        section?.visibleWhen?.value
                      : true;
                  }
                }

                if (
                  dataTransformed[fieldData] &&
                  typeof dataTransformed[fieldData] === "string" &&
                  dataTransformed[fieldData]?.trim() === ""
                ) {
                  setValue(fieldData, "");
                  delete dataTransformed[fieldData];
                }

                if (
                  required &&
                  !(fieldData in dataTransformed) &&
                  visibleWhenUnderSection &&
                  visibleWhen
                ) {
                  sectionResult.missingFieldsCount++;
                  sectionResult.missingFieldsExist = true;
                }
                if (
                  (section?.type === "number" || section.type === "mobile") &&
                  dataTransformed[fieldData]?.numberWithCode &&
                  !isValidPhoneNumber(
                    dataTransformed[fieldData]?.numberWithCode
                  )
                ) {
                  setValue(fieldData, "");
                  delete dataTransformed[fieldData];
                  if (!triggerError) {
                    clearErrors();
                  }
                }
                if (
                  regex &&
                  dataTransformed[fieldData] &&
                  !new RegExp(regex?.value).test(dataTransformed[fieldData])
                ) {
                  sectionResult.missingFieldsCount++;
                  sectionResult.missingFieldsExist = true;
                  sectionResult.isValidateRegex = false;
                  setValue(fieldData, null);
                  delete dataTransformed[fieldData];
                  if (!triggerError) {
                    clearErrors();
                  }
                }
              }
            );
          } else {
            let isMandatory = false;
            if (field?.mandatoryWhen) {
              const mandatoryWhen = field?.mandatoryWhen;
              if (
                mandatoryWhen?.condition === "or" &&
                Array.isArray(mandatoryWhen?.rules)
              ) {
                isMandatory = mandatoryWhen.rules.some((ruleGroup: any) => {
                  if (
                    ruleGroup.condition === "and" &&
                    Array.isArray(ruleGroup.rules)
                  ) {
                    return ruleGroup.rules.every((rule: any) => {
                      const fieldValue =
                        (rule?.subSectionName &&
                          dataTransformed?.[rule.subSectionName]?.some(
                            (obj: any) => obj[rule.fieldName] === rule.value
                          )) ||
                        dataTransformed?.[rule.fieldName] ||
                        watch(rule.fieldName);
                      return typeof fieldValue === "boolean"
                        ? fieldValue
                        : fieldValue?.value === rule.value ||
                            fieldValue === rule.value;
                    });
                  }
                  return false;
                });
              }
            }

            if (
              dataTransformed[fieldName] &&
              typeof dataTransformed[fieldName] === "string" &&
              dataTransformed[fieldName]?.trim() === ""
            ) {
              setValue(fieldName, "");
              delete dataTransformed[fieldName];
            }

            if (
              isRequired &&
              fieldName &&
              !field?.visibleWhen &&
              (dataTransformed[fieldName] === "" ||
                dataTransformed[fieldName] === false)
            ) {
              sectionResult.missingFieldsCount++;
              sectionResult.missingFieldsExist = true;
            }

            const visibleWhen = checkVisibility(field.visibleWhen);

            if (isMandatory && !(fieldName in dataTransformed) && visibleWhen) {
              sectionResult.missingFieldsCount++;
              sectionResult.missingFieldsExist = true;
            }

            if (
              isRequired &&
              !(fieldName in dataTransformed) &&
              visibleWhen &&
              checkVisibleWhen(field?.visibleWhen, dataTransformed)
            ) {
              sectionResult.missingFieldsCount++;
              sectionResult.missingFieldsExist = true;
            }
            // Regex validation
            if (
              regex &&
              dataTransformed[fieldName] &&
              !new RegExp(regex?.value).test(dataTransformed[fieldName])
            ) {
              sectionResult.missingFieldsCount++;
              sectionResult.missingFieldsExist = true;
              sectionResult.isValidateRegex = false;
              setValue(fieldName, null);
              delete dataTransformed[fieldName];
              if (!triggerError) {
                clearErrors();
              }
            }
          }
        });
        resultArray.push(sectionResult);
      }
    });
    return resultArray;
  };
  const handleFormSubmit = async (isSubmit: any) => {
    const selectedData = getValues();

    const copySelectedData = JSON.parse(JSON.stringify(selectedData));

    let dataTransformed = transformDropdowns(copySelectedData);

    try {
      const currentApplicationDetails = await getStudentDetails(
        nextFormDetails?.oap,
        userEmail,
        application,
        apiKey,
        await getAccessToken()
      );

      // Check if application is already submitted
      if (currentApplicationDetails?.applicationStatus === "submitted") {
        setIsAlreadySubmittedModalOpen(true);
        setSaving(false);
        return;
      }
    } catch (error) {
      console.error("Error checking application status:", error);
      // Continue with normal flow if API call fails
    }

    if (isSubmit) {
      setTriggerError(true);
      const result = validateOnSubmit(dataTransformed, true);
      setValidationError(result);
      if (result?.every((item: any) => item.missingFieldsExist === false)) {
        dataTransformed["applicationStatus"] = "submitted";
        const res = await saveOapForm(
          {
            ...dataTransformed,
            sectionLabel: formQuery?.label,
            localization: preferLang,
            applicationUpdateSource: isAppHero
              ? "Updated by AppHero"
              : "Updated by OAP",
            applicationSubmitSource: isAppHero
              ? "Submitted by AppHero"
              : "Submitted by OAP",
          },
          { oapName: nextFormDetails.oap, mode: nextFormDetails.mode },
          apiKey,
          await getAccessToken()
        );
        if (res?.statusCode == 500) {
          toast.custom(
            <div className="bg-error flex items-center gap-x-1 p-2 rounded">
              <div className="w-6">
                <AlertCircle
                  name="shield-alert"
                  height={20}
                  width={20}
                  color={"var(--color-background)"}
                />
              </div>
              <p className="text-background text-sm">
                {staticContents?.errors?.application?.saveFailed ||
                  "There is an error occurred on saving application. Please contact administrator."}
              </p>
            </div>
          );
          setSaving(false);
          return;
        }
        if (res) {
          setNextFormDetails((prev: any) => ({
            ...prev,
            currentOap: apply,
          }));
        }
        setProgress(res?.progress);
        // Get the current query parameters
        const searchParams: any = new URLSearchParams(window.location.search);
        searchParams.set("step", Number(step) + 1);
        const updatedQueryString = searchParams.toString();
        if (route?.length - 1 != step) {
          router.push(`/form?${updatedQueryString}`);
        } else {
          router.replace("/thank-you");
        }
      } else {
        toast.custom(
          <div className="bg-error flex items-center gap-x-1 p-2 rounded">
            <div className="w-6">
              <AlertCircle
                name="shield-alert"
                height={20}
                width={20}
                color={"var(--color-background)"}
              />
            </div>
            <p className="text-background text-sm">
              {staticContents?.errors?.application?.requiredFields ||
                "You still have to fill all required fields"}
            </p>
          </div>
        );
        setSaving(false);
        return;
      }
    } else {
      const result = validateOnSubmit(dataTransformed, triggerError);
      setValidationError(result);
      const res = await saveOapForm(
        {
          email: userEmail,
          applicationId: application,
          applicationStatus: "inProgress",
          ...dataTransformed,
          sectionLabel: formQuery?.label,
          localization: preferLang,
          applicationUpdateSource: isAppHero
            ? "Updated by AppHero"
            : "Updated by OAP",
        },
        { oapName: nextFormDetails.oap, mode: nextFormDetails.mode },
        apiKey,
        await getAccessToken()
      );
      if (res) {
        setNextFormDetails((prev: any) => ({
          ...prev,
          currentOap: apply,
        }));
        refetchStudent();
      }
      setProgress(res?.progress);
      // Get the current query parameters
      const searchParams: any = new URLSearchParams(window.location.search);
      searchParams.set("step", Number(step) + 1);
      const updatedQueryString = searchParams.toString();
      if (isSubmit == false) {
        router.push(`/form?${updatedQueryString}`);
      }
    }
    if (!isSubmit) {
      setSaving(false);
    }
  };

  const getButtonPlaceholder = () => {
    const placeholder = formQuery?.fieldData?.find(
      (item: any) => item?.type === "button"
    )?.placeholder;
    return placeholder;
  };

  const handleFileUpload = async (payload: any, fieldName: any) => {
    try {
      const response: any = await uploadFile({
        email: userEmail,
        applicationId: application,
        oapName: nextFormDetails?.oap,
        businessUnitFilter: studentDetails?.businessUnitFilter,
        payload,
      });

      // Handle case where response.ocrFields might be a Response object instead of parsed JSON
      let ocrFields = response?.ocrFields || {};
      if (ocrFields && typeof ocrFields.json === "function") {
        try {
          ocrFields = await ocrFields.json();
        } catch (parseError) {
          ocrFields = {};
        }
      }

      if ("successOcrFields" in response) {
        // Increment document-specific OCR reprocess count
        const documentOcrField = `ocrReprocessCount_${fieldName}`;
        const currentCount = watch(documentOcrField) || 0;
        const newCount = currentCount + 1;
        setValue(documentOcrField, newCount);
      }

      if (
        response?.progressPercentage ||
        response?.response ||
        response?.status
      ) {
        if (payload?.processOcr === true) {
          if (payload.pickListFields && payload.fieldsToEnable) {

            
          } else if (
            Object.keys(ocrFields).length > 0 &&
            response.successOcrFields
          ) {
            // Use document-specific field names for consistency
            setValue(`isSuccessfullExtraction_${fieldName}`, true);
            setValue(`ocrExtractedResponse_${fieldName}`, ocrFields);
            // Map OCR fields to form fields using direct mapping
            for (const key in ocrFields) {
              setValue(key, ocrFields[key]);
              clearErrors(key);
            }
          } else {
            setValue(`isSuccessfullExtraction_${fieldName}`, false);
          }
        }

        setValue(fieldName, response);
        return {
          success: true,
          document: response.response,
          ocrFields: ocrFields,
          successOcrFields:
            Object.keys(ocrFields).length > 0 && response.successOcrFields,
        };
      } else {
        toast.custom(
          <div className="bg-error flex items-center gap-x-1 p-2 rounded">
            <div className="w-6">
              <AlertCircle
                name="shield-alert"
                height={20}
                width={20}
                color={"var(--color-background)"}
              />
            </div>
            <p className="text-background text-sm">{response?.message}</p>
          </div>
        );
        return { success: false };
      }
    } catch (error) {
      setSaving((prev) => !prev);
      console.log({ error });
      return { success: false };
    }
  };
  const handleDeleteFile = async (payload: any) => {
    setSaving((prev) => !prev);
    await deleteFile({
      payload: payload?.fileData,
      studentDetail: {
        email: userEmail,
        oapName: nextFormDetails?.oap,
        applicationId: application,
        name: payload?.fileData?.name,
        type: payload?.documentType,
        documentId: payload?.fileData?.documentId,
      },
    });
    handleFormSubmit(null);
  };

  const getLabel = (sections: any) => {
    const sectionLength = sections?.length;
    const section = sections?.find(
      (_: any, index: number) => index === Number(step)
    );
    const label = `${Number(step) + 1}/${sectionLength} ${
      section?.displayName
    }`;

    return label;
  };
  const handleAddMore = (fieldItem: any) => {
    const fieldName = fieldItem?.fieldName;

    setSubSectionArrayMap((prevMap) => {
      const prevIndexes = prevMap[fieldName] || [];
      const currentCount =
        prevIndexes.length > 0 ? Math.max(...prevIndexes) + 1 : 0;

      return {
        ...prevMap,
        [fieldName]: [...prevIndexes, currentCount],
      };
    });

    setSubSectionCountMap((prevMap) => ({
      ...prevMap,
      [fieldName]: (prevMap[fieldName] || 0) + 1,
    }));
  };

  const handleRemove = async (index: any, fieldItem: any) => {
    const subSectionName: any = fieldItem?.subSection;
    const fieldName = fieldItem?.fieldName;

    setSubSectionArrayMap((prevMap) => ({
      ...prevMap,
      [fieldName]: (prevMap[fieldName] || []).filter(
        (item: any) => item !== index
      ),
    }));

    setSubSectionCountMap((prevMap) => ({
      ...prevMap,
      [fieldName]: (prevMap[fieldName] || 1) - 1,
    }));

    fieldItem?.[subSectionName]?.fieldData?.forEach(
      (ele: any, eleIndex: number) => {
        if (ele.type == "document" || ele.type == "multiDocument") {
          const ids = watch(fieldName)?.[index]?.[ele?.fieldName];
          if (Array.isArray(ids))
            ids?.forEach((item: any, itemIndex: number) => {
              handleDeleteFile({ fileData: item, documentType: item?.type });
            });
        }
      }
    );

    const fieldRemoved = watch(fieldName)?.filter(
      (_item: any, i: number) => index !== i
    );

    setValue(fieldName, fieldRemoved);
    await handleFormSubmit(null);
  };

  const handleBackButton = () => {
    setNextFormDetails({
      form: "PROGRAM_FILTER",
      mode: process.env.NEXT_PUBLIC_OAP_MODE,
      oap: process.env.NEXT_PUBLIC_OAP_NAME,
      type: "single",
    });
    reset();
    router.push("/application-filter");
  };

  const handleDisableButton = () => {
    const { isConfirmed, isExtracted } = formQuery?.ocrConfirmationFields || {};
    const isExtractionConfirmed = watch(isConfirmed);
    let hasAnySuccessfulExtraction = watch(isExtracted);

    if (formQuery?.isOcrReprocess) {
      if (!hasAnySuccessfulExtraction && formQuery.enableWhen) {
        return checkVisibility(formQuery.enableWhen);
      } else if (!hasAnySuccessfulExtraction && !formQuery.enableWhen) {
        return true;
      } else if (hasAnySuccessfulExtraction) {
        return !isExtractionConfirmed;
      } else {
        // Check if any OCR document is still within reprocess limit
        const hasAnyDocumentWithinLimit = formQuery?.fieldData?.some(
          (field: any) => {
            if (field.isOcrDocument) {
              const documentOcrCount =
                watch(`ocrReprocessCount_${field.fieldName}`) || 0;
              const documentLimit =
                field.maxOcrReprocess || formQuery?.maxOcrReprocess;
              return documentOcrCount < documentLimit;
            }
            return false;
          }
        );

        return hasAnyDocumentWithinLimit && !isExtractionConfirmed;
      }
    }

    return true;
  };

  const handleViewConfirmation = () => {
    router.push("/thank-you");
  };

  const checkVisibility = (enableProps: any) => {
    if (!enableProps) {
      return true;
    }

    if (!enableProps) {
      return true;
    }
    const fieldWatchValue = watch(enableProps.fieldName);
    let isArr = Array.isArray(fieldWatchValue);
    const { condition, value } = enableProps;

    if (Array.isArray(enableProps?.value) && condition !== "notEqual") {
      return enableProps?.value.some((item: string) => {
        if (typeof fieldWatchValue === "string") {
          return item === (fieldWatchValue as string);
        }

        if (typeof fieldWatchValue === "object" && fieldWatchValue !== null) {
          return (
            item === fieldWatchValue?.value || item === fieldWatchValue?.label
          );
        }

        if (Array.isArray(fieldWatchValue)) {
          return fieldWatchValue.some(
            (v: any) => item === v?.value || item === v?.label || item === v
          );
        }

        return false;
      });
    }

    if (
      (enableProps.condition === "notAnd" || enableProps.condition === "and") &&
      Array.isArray(enableProps.rules)
    ) {
      const allRulesTrue = enableProps.rules.every((rule: any) => {
        const value = watch(rule.fieldName);
        const isArr = Array.isArray(value);

        if (typeof value === "object" && !isArr) {
          return value?.value === rule.value || value?.label === rule.value;
        }

        if (isArr) {
          return value.some(
            (v: any) =>
              v?.value === rule.value ||
              v?.label === rule.value ||
              v === rule.value
          );
        }

        return value === rule.value;
      });

      return enableProps.condition === "notAnd" ? !allRulesTrue : allRulesTrue;
    }

    if (condition === "exists") {
      return Array.isArray(fieldWatchValue)
        ? fieldWatchValue.length > 0
        : !!fieldWatchValue;
    }

    if (condition === "notExists") {
      return Array.isArray(fieldWatchValue)
        ? fieldWatchValue.length > 0
        : !fieldWatchValue;
    }

    if (typeof fieldWatchValue == "object" && !isArr) {
      if (condition === "notEqual") {
        return !value.some(
          (item: { value: string }) => item.value === fieldWatchValue?.value
        );
      }
      if (condition === "equal") {
        return (
          value.some(
            (item: { value: string }) => item.value === fieldWatchValue?.value
          ) ||
          value.some(
            (item: { label: string }) => item.label === fieldWatchValue?.label
          )
        );
      }
      if (fieldWatchValue?.value == enableProps?.value) return true;
      return false;
    }
    if (enableProps?.value) {
      if (condition === "notEqual") {
        // For notEqual condition: field should be enabled only if a value is selected AND it's not in the excluded values
        return (
          fieldWatchValue &&
          !(
            enableProps?.value.some(
              (item: { value: string }) => item.value === fieldWatchValue
            ) ||
            (isArr &&
              fieldWatchValue.some((obj: any) =>
                value.some(
                  (item: { value: string }) => item.value === obj.value
                )
              )) ||
            (Array.isArray(enableProps?.value) &&
              enableProps?.value.includes(fieldWatchValue))
          )
        );
      }
      if (condition === "equal") {
        return (
          enableProps?.value.some(
            (item: { value: string }) => item.value === fieldWatchValue
          ) ||
          (isArr &&
            fieldWatchValue.some((obj: any) =>
              value.some((item: { value: string }) => item.value === obj.value)
            )) ||
          (Array.isArray(enableProps?.value) &&
            enableProps?.value.includes(fieldWatchValue))
        );
      }
      return fieldWatchValue === enableProps?.value ||
        (isArr &&
          fieldWatchValue?.some((obj: any) => obj.value === enableProps?.value))
        ? true
        : false;
    }
    return true;
  };

  return (
    <>
      <div className="hidden lg:flex md:flex min-w-[316px] p-4 pl-5 h-screen bg-on-background flex-col justify-between overflow-y-scroll">
        <div>
          <TimelineSession
            validationError={validationError}
            progressValue={progress || studentDetails?.progress}
            getSideBarStatus={getSideBarStatus}
            handleFormSubmit={handleFormSubmit}
            allSections={allSections}
            appId={studentDetails?.appId}
            canDisableTabs={formQueryIsFetching || sectionQueryIsFetching}
            triggerError={triggerError}
            completedText={sectionQuery?.progressInfo?.displayName}
          />
        </div>
        {!isAppHero ? (
          <div
            className={`text-text-secondary w-fit pr-4
           cursor-pointer flex justify-between items-center gap-x-4`}
          >
            <p
              onClick={async () => {
                await signOut();
                localStorage.clear();
                sessionStorage.removeItem("utmParams");
                router.push("/login");
              }}
              className="underline hover:opacity-100 opacity-50"
            >
              {staticContents?.application?.logout || "Logout"}
            </p>
            {sectionQuery?.languageData &&
              sectionQuery?.languageData?.canShowLanguageSwitch && (
                <LanguageSelector languageData={sectionQuery?.languageData} />
              )}
          </div>
        ) : null}
      </div>
      <div className="flex min-h-screen flex-col overflow-y-scroll w-full">
        <div className="sticky top-0 h-20 z-50 pt-0">
          <Header
            logo={sectionQuery?.displayNameLogo}
            sectionQueryIsFetching={sectionQueryIsFetching}
            image={sectionQuery?.logoInfo?.signedUrl}
            logoInfo={sectionQuery?.logoInfo}
            title={
              studentDetails?.[sectionQuery?.displayName] ||
              studentDetails?.[sectionQuery?.displayNameField]
            }
            handleBackButton={handleBackButton}
          />
        </div>

        <div
          className={`lg:hidden md:hidden pt-6 pb-3 bg-primary items-center pl-5 `}
          onClick={() => {
            setIsDialogOpen(true);
          }}
        >
          {formQueryIsFetching || sectionQueryIsFetching ? (
            <div className="flex w-full">
              <Image
                priority
                src={loader2}
                height={32}
                width={32}
                style={{ objectFit: "contain" }}
                alt="loader"
              />
            </div>
          ) : (
            <div className="flex w-full items-center justify-between py-1">
              <Label className="text-background text-center pl-4">
                {getLabel(allSections)}
              </Label>
              <ChevronDown className="h-6 w-6 mr-4 " color="white" />
            </div>
          )}
        </div>

        {isDialogOpen ? (
          <div className=" fixed  left-0 h-screen w-full bg-gray-600 flex flex-col justify-center items-center bg-opacity-50 z-50">
            <div
              className=" bg-primary py-10 px-10 relative min-[378px]:w-[80%]"
              style={{ overflowY: "scroll" }}
            >
              <div
                className="absolute top-4 right-4 bg-white h-4 w-4 flex items-center justify-center cursor-pointer"
                onClickCapture={() => {
                  setIsDialogOpen(false);
                }}
              >
                <Label className="text-onNeutral text-center text-sm cursor-pointer">
                  x
                </Label>
              </div>
              <MobileTimelineSession
                validationError={validationError}
                items={allSections}
                setIsDialogOpen={setIsDialogOpen}
                progressValue={progress || studentDetails?.progress}
                getSideBarStatus={getSideBarStatus}
                handleFormSubmit={handleFormSubmit}
                appId={studentDetails?.appId}
              />
            </div>
          </div>
        ) : null}

        {formQueryIsFetching || sectionQueryIsFetching ? (
          <div className="pt-[35px] pb-[120px] pl-4 pr-4 md:pl-20 md:pr-4  mt-4 flex h-full flex-col overflow-scroll">
            {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 12, 13, 14].map(
              (_item: number, i: number) => (
                <div key={`skeleton_${i}`}>
                  <Skeleton className="w-[40%] lg:w-[20%] h-[20px] rounded-md bg-border mb-2 mt-2" />
                  <Skeleton className="lg:w-[50%] h-[40px] rounded-md bg-border mt-2" />
                </div>
              )
            )}
          </div>
        ) : (
          <div className=" sm:w-[100%] md:w-[100%] lg:w-[55%] pl-4 pr-4 md:pl-20 md:pr-4 md:pt-[40px] pb-[45px]  mt-4">
            <form>
              <Label
                className="font-bold text-3xl"
                style={getBrandSpecificFontStyle(fontSize, "page-title")}
              >
                {formQuery?.displayName}
              </Label>

              <div>
                <div className="flex justify-between"></div>
                <FormContainer
                  register={register}
                  fieldData={formQuery?.fieldData}
                  studentDetails={studentDetails}
                  trigger={trigger}
                  setError={setError}
                  clearErrors={clearErrors}
                  watch={watch}
                  errors={errors}
                  setValue={setValue}
                  uploadDocs={handleFileUpload}
                  handleDeleteFile={handleDeleteFile}
                  handleAddMore={handleAddMore}
                  handleRemove={handleRemove}
                  subSectionArrayMap={subSectionArrayMap}
                  popupDetails={formQuery?.popupDetails}
                  formQuery={formQuery}
                  enableAutoDetection={enableAutoDetection}
                />
              </div>

              <div
                className={` w-fit text-background rounded bg-secondary hover:bg-primary font-bold text-sm px-5 me-2 mb-4 cursor-pointer py-2.5 ${
                  formQuery &&
                  formQuery?.isOcrReprocess &&
                  checkVisibility(formQuery?.enableButtonWhen) &&
                  handleDisableButton()
                    ? "opacity-50 cursor-not-allowed"
                    : ""
                }`}
                style={{
                  ...getBrandSpecificFontStyle(fontSize, "label"),
                }}
                onClick={() => {
                  if (saving) return;

                  if (
                    formQuery &&
                    formQuery?.isOcrReprocess &&
                    checkVisibility(formQuery?.enableButtonWhen) &&
                    handleDisableButton()
                  )
                    return;

                  const buttonFieldItem = formQuery?.fieldData?.find(
                    (item: any) => item?.type === "button"
                  );

                  const updateWhen = buttonFieldItem?.updateWhen
                    ? watch(buttonFieldItem?.updateWhen?.fieldName) ===
                      buttonFieldItem?.updateWhen?.value
                    : true;

                  if (buttonFieldItem?.fieldName && updateWhen) {
                    setValue(
                      buttonFieldItem?.fieldName,
                      buttonFieldItem?.value
                    );
                  }

                  setSaving((prev) => !prev);
                  handleFormSubmit(
                    getButtonPlaceholder() === "Submit" ||
                      getButtonPlaceholder() === "Abschicken"
                      ? true
                      : false
                  );
                }}
              >
                {saving ? (
                  <div className=" w-full  flex items-center justify-center">
                    <Image
                      priority
                      src={loader2}
                      height={20}
                      width={20}
                      alt="loader image"
                    />
                  </div>
                ) : (
                  <p>{getButtonPlaceholder()}</p>
                )}
              </div>
            </form>
          </div>
        )}
      </div>

      {/* Already Submitted Modal */}
      <AlreadySubmittedModal
        isOpen={isAlreadySubmittedModalOpen}
        onClose={() => setIsAlreadySubmittedModalOpen(false)}
        onViewConfirmation={handleViewConfirmation}
        details={
          sectionQuery?.alreadySubmittedModalDetails || {
            buttonText: "View Confirmation",
            message:
              "You’ve already submitted this application. Please check your confirmation for details.",
            title: "Application Already Submitted",
          }
        }
      />

      {/* Saving Modal */}
      <SavingModal isOpen={saving} />
    </>
  );
}
export default SectionalForm;
